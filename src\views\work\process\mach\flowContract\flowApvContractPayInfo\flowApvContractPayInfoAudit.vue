<template>
  <!--
    组件：付款审批-审批页面
    路径：src\views\work\process\mach\flowContract\flowApvContractPayInfo\flowApvContractPayInfoAudit.vue
  -->
  <basic-container>
    <el-tabs v-model="flowTabSelected">
      <el-tab-pane label="流程信息" name="flow">
        <el-row>
          <el-col :span="24">
            <histoicFlowVue :processInstanceId="processInstanceId" :taskId="taskId" />
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="流程图" name="diagram">
        <el-row>
          <el-col :span="24">
            <flowDiagramVue :processInstanceId="processInstanceId" />
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="表单信息" name="form">
        <el-row>
          <el-col :span="24">
            <!-- 流程信息卡片 -->
            <mach-card :open="true" style="margin-bottom: 16px;">
              <template #title>
                <el-icon size="20"><User /></el-icon>
                <el-text size="large">流程信息</el-text>
              </template>
              <el-descriptions :column="3" border>
                <el-descriptions-item label="流程名称">
                  {{ getProcessName() }}
                </el-descriptions-item>
                <el-descriptions-item label="当前环节">
                  <el-tag type="warning">
                    {{ props.taskName || '--' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="当前状态">
                  <el-tag :type="getStatusType(form.flowApvContractPayInfo?.status)">
                    {{ getStatusName(form.flowApvContractPayInfo?.status) }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="创建人">
                  {{ form.flowApvContractPayInfo?.createUserName || '--' }}
                </el-descriptions-item>
                <el-descriptions-item label="创建时间">
                  {{ formatDateTime(form.flowApvContractPayInfo?.createTime) }}
                </el-descriptions-item>
                <el-descriptions-item label="流程实例ID">
                  {{ props.processInstanceId || '--' }}
                </el-descriptions-item>
                <el-descriptions-item label="审批人" v-if="form.flowApvContractPayInfo?.approverName">
                  {{ form.flowApvContractPayInfo.approverName }}
                </el-descriptions-item>
                <el-descriptions-item label="付款人" v-if="form.flowApvContractPayInfo?.payerName">
                  {{ form.flowApvContractPayInfo.payerName }}
                </el-descriptions-item>
                <el-descriptions-item label="更新时间" v-if="form.flowApvContractPayInfo?.updateTime">
                  {{ formatDateTime(form.flowApvContractPayInfo.updateTime) }}
                </el-descriptions-item>
              </el-descriptions>
            </mach-card>

            <!-- 基本信息卡片 -->
            <mach-card :open="true" style="margin-bottom: 16px;">
              <template #title>
                <el-icon size="20"><InfoFilled /></el-icon>
                <el-text size="large">基本信息</el-text>
              </template>
              <template #extra>
                <el-button type="primary" icon="el-icon-printer" @click="handlePrint">打印</el-button>
              </template>
              <el-descriptions :column="3" border>
                <el-descriptions-item label="编号">
                  {{ form.flowApvContractPayInfo?.code || '--' }}
                </el-descriptions-item>
                <el-descriptions-item label="甲方">
                  {{ getPartyAName(form.flowApvContractPayInfo?.partyA) }}
                </el-descriptions-item>
                <el-descriptions-item label="收款单位">
                  {{ form.flowApvContractPayInfo?.payee || '--' }}
                </el-descriptions-item>
                <el-descriptions-item label="单位简称">
                  {{ form.flowApvContractPayInfo?.payeeSimple || '--' }}
                </el-descriptions-item>
                <el-descriptions-item label="紧急程度">
                  {{ getLevelName(form.flowApvContractPayInfo?.level) }}
                </el-descriptions-item>
                <el-descriptions-item label="款项用途">
                  {{ form.flowApvContractPayInfo?.fundsUse || '--' }}
                </el-descriptions-item>
                <el-descriptions-item label="付款金额" :span="2">
                  <el-text type="primary" size="large" style="font-weight: bold;">
                    ￥{{ formatAmount(form.flowApvContractPayInfo?.amount) }}
                  </el-text>
                </el-descriptions-item>
                <el-descriptions-item label="支出说明" :span="3">
                  {{ form.flowApvContractPayInfo?.payDesc || '--' }}
                </el-descriptions-item>
              </el-descriptions>
            </mach-card>

            <!-- 银行信息卡片 -->
            <mach-card :open="true" style="margin-bottom: 16px;">
              <template #title>
                <el-icon size="20"><CreditCard /></el-icon>
                <el-text size="large">银行信息</el-text>
              </template>
              <el-descriptions :column="3" border>
                <el-descriptions-item label="银行信息是否变更">
                  {{ form.flowApvContractPayInfo?.bankChange === '1' ? '是' : '否' }}
                </el-descriptions-item>
                <el-descriptions-item label="开户行">
                  {{ form.flowApvContractPayInfo?.bankAddress || '--' }}
                </el-descriptions-item>
                <el-descriptions-item label="账号">
                  {{ form.flowApvContractPayInfo?.bankAccount || '--' }}
                </el-descriptions-item>
              </el-descriptions>
            </mach-card>

            <!-- 其他信息卡片 -->
            <mach-card :open="true" style="margin-bottom: 16px;">
              <template #title>
                <el-icon size="20"><Edit /></el-icon>
                <el-text size="large">其他信息</el-text>
              </template>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="是否开具发票">
                  {{ form.flowApvContractPayInfo?.invoiceStatus === '1' ? '是' : '否' }}
                </el-descriptions-item>
                <el-descriptions-item label="货物是否入库">
                  {{ form.flowApvContractPayInfo?.instoreStatus === '1' ? '是' : '否' }}
                </el-descriptions-item>
                <el-descriptions-item label="发票备注" :span="2">
                  {{ form.flowApvContractPayInfo?.invoiceRemark || '--' }}
                </el-descriptions-item>
                <el-descriptions-item label="入库备注" :span="2">
                  {{ form.flowApvContractPayInfo?.instoreRemark || '--' }}
                </el-descriptions-item>
                <el-descriptions-item label="支付方式说明" :span="2">
                  {{ form.flowApvContractPayInfo?.paytypeDesc || '--' }}
                </el-descriptions-item>
                <el-descriptions-item label="备注信息" :span="2">
                  {{ form.flowApvContractPayInfo?.remarks || '--' }}
                </el-descriptions-item>
              </el-descriptions>
            </mach-card>

            <!-- 附件信息卡片 -->
            <mach-card :open="true" style="margin-bottom: 16px;" v-if="form.flowApvContractPayInfo?.attachments">
              <template #title>
                <el-icon size="20"><Files /></el-icon>
                <el-text size="large">附件信息</el-text>
              </template>
              <el-descriptions :column="1" border>
                <el-descriptions-item label="附件">
                  <div v-if="form.flowApvContractPayInfo.attachments">
                    <el-link type="primary" :href="getAttachmentUrl(form.flowApvContractPayInfo.attachments)" target="_blank">
                      <el-icon><Document /></el-icon>
                      查看附件
                    </el-link>
                  </div>
                  <span v-else>--</span>
                </el-descriptions-item>
              </el-descriptions>
            </mach-card>

            <!-- 支付方式信息卡片 -->
            <mach-card :open="true" style="margin-bottom: 16px;" v-if="form.flowApvContractPayInfo?.payDetailList?.length > 0">
              <template #title>
                <el-icon size="20"><Wallet /></el-icon>
                <el-text size="large">支付方式</el-text>
              </template>
              <el-table :data="form.flowApvContractPayInfo.payDetailList" border stripe>
                <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
                <el-table-column label="支付方式" align="center">
                  <template #default="{ row }">
                    {{ getPayTypeName(row.payType) }}
                  </template>
                </el-table-column>
                <el-table-column label="金额" align="center">
                  <template #default="{ row }">
                    <el-text type="primary" style="font-weight: bold;">
                      ￥{{ formatAmount(row.amount) }}
                    </el-text>
                  </template>
                </el-table-column>
                <el-table-column label="备注" align="center">
                  <template #default="{ row }">
                    {{ row.remark || '--' }}
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px; text-align: right;">
                <el-text type="info">
                  支付方式总计：<el-text type="primary" style="font-weight: bold;">￥{{ getPayDetailTotal() }}</el-text>
                </el-text>
              </div>
            </mach-card>

            <!-- 请款单列表 -->
            <mach-card :open="true" v-if="form.flowApvContractPayInfo?.paymentRealList?.length > 0">
              <template #title>
                <el-icon size="20"><List /></el-icon>
                <el-text size="large">请款单列表</el-text>
              </template>
              <el-table :data="form.flowApvContractPayInfo.paymentRealList" border stripe>
                <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
                <el-table-column prop="partyB" label="收款方" min-width="120"></el-table-column>
                <el-table-column prop="contractCode" label="合同编号" min-width="120"></el-table-column>
                <el-table-column label="合同总额" width="120" align="center">
                  <template #default="{ row }">
                    ￥{{ formatAmount(row.eqAmount) }}
                  </template>
                </el-table-column>
                <el-table-column label="付款金额" width="120" align="center">
                  <template #default="{ row }">
                    <el-text type="primary" style="font-weight: bold;">
                      ￥{{ formatAmount(row.eqPaymentAmount) }}
                    </el-text>
                  </template>
                </el-table-column>
                <el-table-column prop="remarks" label="备注" min-width="150"></el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-text type="primary">共 {{ form.flowApvContractPayInfo.paymentRealList.length }} 条请款单</el-text>
                <el-text type="primary" style="margin-left: 20px;">
                  总金额：<el-text style="font-weight: bold;">￥{{ getPaymentRealTotal() }}</el-text>
                </el-text>
              </div>
            </mach-card>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="合同附件" name="pdf">
        <el-row>
          <el-col :span="24">
            <div v-if="pdfLoading" style="text-align: center; padding: 50px;">
              <el-icon class="is-loading"><Document /></el-icon>
              <p>正在加载合同附件...</p>
            </div>
            <div v-else-if="pdfError" style="text-align: center; padding: 50px;">
              <el-icon><Document /></el-icon>
              <p>合同附件加载失败</p>
            </div>
            <machPdf v-else-if="pdfSrc" :src="pdfSrc" />
            <div v-else style="text-align: center; padding: 50px;">
              <el-icon><Document /></el-icon>
              <p>暂无合同附件</p>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>

    <!-- 审批意见区域 - 根据任务状态动态显示 -->
    <template v-if="showAuditSection">
      <el-divider content-position="left">审批意见</el-divider>

      <avue-form :option="auditOption" v-model="auditForm" ref="auditFormRef">
        <!-- 隐藏avue-form自带的操作按钮 -->
        <template #menu-form="{}">
          <!-- 不显示任何内容，隐藏清空和提交按钮 -->
        </template>
      </avue-form>

      <el-form-item style="margin-bottom: 0;">
        <el-button type="primary" icon="Check" @click="handleSubmit" :loading="submitLoading">提交</el-button>
        <el-button icon="Close" @click="handleCancel">关闭</el-button>
      </el-form-item>
    </template>

    <!-- 已办理任务只显示关闭按钮 -->
    <template v-else>
      <el-form-item style="margin-bottom: 0;">
        <el-button icon="Close" @click="handleCancel">关闭</el-button>
      </el-form-item>
    </template>
  </basic-container>
</template>

<script setup>
  import { ref, getCurrentInstance, computed, onMounted, watch } from 'vue';
  import { useStore } from 'vuex';
  import { Document, User, InfoFilled, CreditCard, Edit, Files, Wallet, List } from '@element-plus/icons-vue';
  import histoicFlowVue from '@/views/work/process/mach/histoicFlow/histoicFlow.vue';
  import flowDiagramVue from '@/views/work/process/mach/flow-diagram/flow-diagram.vue';
  import machPdf from "@/components/mach/pdf/mach-pdf.vue";
  import MachCard from "@/components/mach/card/mach-card.vue";
  import { auditFormDetail, audit } from "@/api/mach/pm/flowApvContractPayInfo/apvContractPayInfo";
  import { moneyInterval } from "@/utils/util";

  // 获取this
  let { proxy } = getCurrentInstance()

  // 属性
  const props = defineProps({
    taskId:{},
    processInstanceId:{},
    businessId:{},
    processDefinitionId:{},
    taskDefinitionKey:{},
    status:{},
    taskName:{},
    param:{},
  });

  const store = useStore();
  const userInfo = computed(() => store.state.user.userInfo);
  const permission = computed(() => store.state.user.permission);

  const flowTabSelected = ref("flow");
  const submitLoading = ref(false);
  const auditFormRef = ref(null);
  const pdfLoading = ref(false);
  const pdfError = ref(false);
  const pdfSrc = ref("");
  const form = ref({
    flowApvContractPayInfo: {
      payDetailList: [],
      paymentRealList: []
    }
  });
  const auditOption = ref({});
  const auditForm = ref({
    comment: "",
    flag: "1",
    backTaskKey: ""
  });

  // 控制审批区域显示 - 根据任务状态判断
  const showAuditSection = computed(() => {
    // 如果没有taskId，说明是查看已办理任务，不显示审批区域
    return props.taskId && props.taskId !== null && props.taskId !== '';
  });

  // 采购流程可驳回的节点列表 (type=1)
  const mpCanBackTaskKeyList = [
    {label: '合同责任人',     value: 'contract_resp',        sort: 1},
    {label: '供应链中心',     value: 'supply_chain_center',  sort: 2},
    {label: '分管高管',       value: 'senior',               sort: 3},
    {label: '财务经理',       value: 'fpd',                  sort: 4},
  ];

  // 安装流程可驳回的节点列表 (type=2)
  const iwpCanBackTaskKeyList = [
    {label: '安装合同责任人',   value: 'install_contract_holder', sort: 1},
    {label: '实施服务中心主任', value: 'isc_director',           sort: 2},
    {label: '常务副总',        value: 'ev',                     sort: 3},
    {label: '计财部',        value: 'fpd',                    sort: 4},
    {label: '总经理',          value: 'gm',                     sort: 5},

  ];

  // 可驳回的节点（动态计算）
  const computeTaskKeyList = computed(() => {
    // 根据流程类型选择对应的可驳回节点列表
    let canBackTaskKeyList = [];

    // 从流程定义ID或表单数据中判断流程类型
    if (props.processDefinitionId && props.processDefinitionId.includes('mp_payment')) {
      // 物资采购合同付款流程
      canBackTaskKeyList = mpCanBackTaskKeyList;
    } else if (props.processDefinitionId && props.processDefinitionId.includes('install_works_payment')) {
      // 安装工程合同付款流程
      canBackTaskKeyList = iwpCanBackTaskKeyList;
    } else if (form.value.flowApvContractPayInfo?.type === '1') {
      // 根据type字段判断：采购
      canBackTaskKeyList = mpCanBackTaskKeyList;
    } else if (form.value.flowApvContractPayInfo?.type === '2') {
      // 根据type字段判断：安装
      canBackTaskKeyList = iwpCanBackTaskKeyList;
    } else {
      // 默认使用采购流程的节点列表
      canBackTaskKeyList = mpCanBackTaskKeyList;
    }

    // 根据当前节点确定可驳回的节点
    let currTaskKeys = canBackTaskKeyList.filter(item => {
      return item.value === props.taskDefinitionKey;
    });

    if (currTaskKeys.length === 0) {
      // 如果当前节点不在列表中，返回所有之前的节点
      return canBackTaskKeyList;
    }

    let currentSort = currTaskKeys[0].sort;
    // 只能驳回到当前节点之前的节点
    return canBackTaskKeyList.filter(item => {
      return item.sort < currentSort;
    });
  });

  // 监听审批结果变化，控制驳回节点显示
  watch(() => auditForm.value.flag, (newValue, oldValue) => {
    let backTaskKeyOption = proxy.findObject(auditOption.value.column, 'backTaskKey');
    if (backTaskKeyOption) {
      backTaskKeyOption.display = newValue === "0";
    }
  });

  // 这些配置项已经不需要了，因为我们使用了新的布局方式
  /*
  const payinfoOption = ref({
    submitBtn: false,
    emptyBtn: false,
    labelWidth: 120,
    column: [
      {
        label: "基本信息",
        prop: "baseInfo",
        icon: "el-icon-info",
        collapse: false,  // 默认展开，不显示箭头
        column: [
          {
            label: "编号",
            prop: "code",
            span: 8
          },
          {
            label: "甲方",
            prop: "partyA",
            type: "select",
            span: 8,
            dicData: [
              {
                label: "北京",
                value: "3"
              },
              {
                label: "江苏",
                value: "2"
              },
              {
                label: "天津",
                value: "1"
              }
            ]
          },
          {
            label: "收款单位",
            prop: "payee",
            span: 8
          },
          {
            label: "单位简称",
            prop: "payeeSimple",
            span: 8
          },
          {
            label: "紧急程度",
            prop: "level",
            type: "select",
            span: 8,
            dicData: [
              {
                label: "一般",
                value: "1"
              },
              {
                label: "紧急",
                value: "2"
              },
              {
                label: "特急",
                value: "3"
              }
            ]
          },
          {
            label: "款项用途",
            prop: "fundsUse",
            span: 8
          },
          {
            label: "支出说明",
            prop: "payDesc",
            span: 24
          },
          {
            label: "付款金额",
            prop: "amount",
            span: 8,
            formatter: (val) => {
              return "￥" + formatAmount(val);
            }
          }
        ]
      },
      {
        label: "银行信息",
        prop: "bankInfo",
        icon: "el-icon-bank-card",
        collapse: false,  // 默认展开，不显示箭头
        column: [
          {
            label: "银行信息是否变更",
            prop: "bankChange",
            type: "select",
            span: 8,
            dicData: [
              {
                label: "否",
                value: "0"
              },
              {
                label: "是",
                value: "1"
              }
            ]
          },
          {
            label: "开户行",
            prop: "bankAddress",
            span: 8
          },
          {
            label: "账号",
            prop: "bankAccount",
            span: 8
          }
        ]
      },
      {
        label: "其他信息",
        prop: "otherInfo",
        icon: "el-icon-document",
        collapse: false,  // 默认展开，不显示箭头
        column: [
          {
            label: "是否开具发票",
            prop: "invoiceStatus",
            type: "select",
            span: 8,
            dicData: [
              {
                label: "否",
                value: "0"
              },
              {
                label: "是",
                value: "1"
              }
            ]
          },
          {
            label: "发票备注",
            prop: "invoiceRemark",
            span: 16
          },
          {
            label: "货物是否入库",
            prop: "instoreStatus",
            type: "select",
            span: 8,
            dicData: [
              {
                label: "否",
                value: "0"
              },
              {
                label: "是",
                value: "1"
              }
            ]
          },
          {
            label: "入库备注",
            prop: "instoreRemark",
            span: 16
          },
          {
            label: "支付方式说明",
            prop: "paytypeDesc",
            span: 24
          },
          {
            label: "备注信息",
            prop: "remarks",
            span: 24
          }
        ]
      }
    ]
  });

  // 支付方式-列表
  const paymentOption = ref({
    height: 'auto',
    calcHeight: 30,
    searchShow: false,
    border: true,
    index: true,
    selection: false,
    viewBtn: false,
    editBtn: false,
    delBtn: false,
    addBtn: false,
    menu: false,
    column: [
      {
        label: "支付方式",
        prop: "payType",
        type: "select",
        dicData: [
          {
            label: "现金",
            value: "1"
          },
          {
            label: "银行转账",
            value: "2"
          },
          {
            label: "支票",
            value: "3"
          }
        ]
      },
      {
        label: "金额",
        prop: "amount",
        type: "number",
        precision: 2
      },
      {
        label: "备注",
        prop: "remark"
      }
    ]
  });

  function formatAmount(a){
    return moneyInterval(a);
  }
  */

  // 打印
  function handlePrint() {
    window.print();
  }

  // 格式化金额
  function formatAmount(amount) {
    if (!amount && amount !== 0) return '0.00';
    return Number(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }

  // 获取甲方名称
  function getPartyAName(partyA) {
    const partyAMap = {
      '1': '天津',
      '2': '江苏',
      '3': '北京'
    };
    return partyAMap[partyA] || '--';
  }

  // 获取紧急程度名称
  function getLevelName(level) {
    const levelMap = {
      '1': '一般',
      '2': '紧急',
      '3': '特急'
    };
    return levelMap[level] || '--';
  }

  // 获取支付方式名称
  function getPayTypeName(payType) {
    const payTypeMap = {
      '1': '现金',
      '2': '银行转账',
      '3': '支票',
      '4': '其他'
    };
    return payTypeMap[payType] || '--';
  }

  // 格式化日期时间
  function formatDateTime(dateTime) {
    if (!dateTime) return '--';
    const date = new Date(dateTime);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  // 获取状态名称
  function getStatusName(status) {
    const statusMap = {
      '0': '草稿',
      '1': '审批中',
      '2': '已通过',
      '3': '已驳回',
      '4': '已撤销'
    };
    return statusMap[status] || '未知';
  }

  // 获取状态类型（用于el-tag的type属性）
  function getStatusType(status) {
    const statusTypeMap = {
      '0': 'info',      // 草稿 - 灰色
      '1': 'warning',   // 审批中 - 橙色
      '2': 'success',   // 已通过 - 绿色
      '3': 'danger',    // 已驳回 - 红色
      '4': 'info'       // 已撤销 - 灰色
    };
    return statusTypeMap[status] || 'info';
  }

  // 获取附件URL
  function getAttachmentUrl(attachments) {
    if (!attachments) return '#';
    // 如果attachments是字符串，直接返回
    if (typeof attachments === 'string') {
      return attachments;
    }
    // 如果是对象或数组，尝试获取第一个附件的URL
    if (Array.isArray(attachments) && attachments.length > 0) {
      return attachments[0].url || attachments[0].link || '#';
    }
    return '#';
  }

  // 获取流程名称
  function getProcessName() {
    // 根据流程定义ID或其他标识来确定流程名称
    if (props.processDefinitionId) {
      // 如果包含付款审批相关的关键字
      if (props.processDefinitionId.includes('contractPay') || props.processDefinitionId.includes('ApvContractPay')) {
        return '合同付款审批流程';
      }
      // 可以根据实际的流程定义ID来扩展更多流程名称
      return '付款审批流程';
    }
    return '付款审批流程';
  }

  // 计算支付方式总金额
  function getPayDetailTotal() {
    if (!form.value.flowApvContractPayInfo?.payDetailList) return '0.00';
    const total = form.value.flowApvContractPayInfo.payDetailList.reduce((sum, item) => {
      return sum + (Number(item.amount) || 0);
    }, 0);
    return formatAmount(total);
  }

  // 计算请款单总金额
  function getPaymentRealTotal() {
    if (!form.value.flowApvContractPayInfo?.paymentRealList) return '0.00';
    const total = form.value.flowApvContractPayInfo.paymentRealList.reduce((sum, item) => {
      return sum + (Number(item.eqPaymentAmount) || 0);
    }, 0);
    return formatAmount(total);
  }

  // 生成合同附件预览
  function generatePdfPreview() {
    console.log('开始生成合同附件预览...');
    console.log('form.value:', form.value);

    if (!form.value.flowApvContractPayInfo || !form.value.flowApvContractPayInfo.id) {
      console.error('合同附件预览失败: 缺少付款审批ID');
      pdfError.value = true;
      return;
    }

    pdfLoading.value = true;
    pdfError.value = false;

    // 构建合同附件预览URL - 使用viewAttachmentsPdfOutStream接口
    const pdfUrl = `/blade-apvContractPayInfo/apvContractPayInfo/viewAttachmentsPdfOutStream?id=${form.value.flowApvContractPayInfo.id}`;
    console.log('合同附件预览URL:', pdfUrl);
    pdfSrc.value = pdfUrl;

    // 模拟加载完成
    setTimeout(() => {
      pdfLoading.value = false;
      console.log('合同附件预览加载完成');
    }, 1000);
  }

  // 审核提交
  async function handleSubmit() {
    auditFormRef.value.clearValidate();

    // form本身校验
    let formValidRes = await new Promise((resolve) =>{
        auditFormRef.value.validate((valid, done)=>{
            resolve(valid);
            done();
        })
    });

    if(formValidRes == false){
        proxy.$Message.warning("请完成校验项");
        return false;
    }

    // 获取流程定义Key - 从processDefinitionId中提取
    let processDefinitionKey = '';
    if (props.processDefinitionId) {
        // processDefinitionId格式通常是 "key:version:id"，我们需要提取key部分
        const parts = props.processDefinitionId.split(':');
        if (parts.length >= 1) {
            processDefinitionKey = parts[0];
        }
    }

    // 构建提交参数 - 确保包含所有必要字段
    let params = {
        // 基本信息
        id: form.value.flowApvContractPayInfo.id,
        code: form.value.flowApvContractPayInfo.code,
        type: form.value.flowApvContractPayInfo.type,
        partyA: form.value.flowApvContractPayInfo.partyA,
        amount: form.value.flowApvContractPayInfo.amount,
        payee: form.value.flowApvContractPayInfo.payee,
        payeeSimple: form.value.flowApvContractPayInfo.payeeSimple,
        level: form.value.flowApvContractPayInfo.level,
        fundsUse: form.value.flowApvContractPayInfo.fundsUse,
        payDesc: form.value.flowApvContractPayInfo.payDesc,

        // 银行信息
        bankChange: form.value.flowApvContractPayInfo.bankChange,
        bankAddress: form.value.flowApvContractPayInfo.bankAddress,
        bankAccount: form.value.flowApvContractPayInfo.bankAccount,

        // 其他信息
        invoiceStatus: form.value.flowApvContractPayInfo.invoiceStatus,
        invoiceRemark: form.value.flowApvContractPayInfo.invoiceRemark,
        instoreStatus: form.value.flowApvContractPayInfo.instoreStatus,
        instoreRemark: form.value.flowApvContractPayInfo.instoreRemark,
        paytypeDesc: form.value.flowApvContractPayInfo.paytypeDesc,
        remarks: form.value.flowApvContractPayInfo.remarks,
        // 处理附件字段：如果是数组则转换为字符串
        attachments: Array.isArray(form.value.flowApvContractPayInfo.attachments)
            ? form.value.flowApvContractPayInfo.attachments.join(',')
            : (form.value.flowApvContractPayInfo.attachments || ''),

        // 流程信息
        procInsId: form.value.flowApvContractPayInfo.procInsId,
        flow: {
            taskId: props.taskId,
            processInstanceId: props.processInstanceId,
            businessId: props.businessId,
            processDefinitionId: props.processDefinitionId,
            processDefinitionKey: processDefinitionKey, // 添加流程定义Key
            taskDefinitionKey: props.taskDefinitionKey,
            status: props.status,
            flag: auditForm.value.flag,
            comment: auditForm.value.comment,
        },
        backTaskKey: auditForm.value.backTaskKey,

        // 支付方式列表
        payDetailList: form.value.flowApvContractPayInfo.payDetailList || [],

        // 请款单列表 - 处理其中的文件字段
        paymentRealList: (form.value.flowApvContractPayInfo.paymentRealList || []).map(item => ({
            ...item,
            // 确保文件字段是字符串格式
            contractFiles: Array.isArray(item.contractFiles)
                ? item.contractFiles.join(',')
                : (item.contractFiles || ''),
            attachmentFiles: Array.isArray(item.attachmentFiles)
                ? item.attachmentFiles.join(',')
                : (item.attachmentFiles || '')
        }))
    };

    console.log('提交参数:', params);
    console.log('流程定义Key:', processDefinitionKey);
    console.log('付款审批信息ID:', params.id);
    console.log('流程实例ID:', params.procInsId);
    console.log('处理后的附件字段:', params.attachments);
    console.log('处理后的请款单列表:', params.paymentRealList);

    submitLoading.value = true;
    audit(params).then(resp => {
      submitLoading.value = false;
      proxy.$Message({message: resp.data.msg, type: "success"});
      handleCancel();
    }).catch(error => {
      console.error('提交失败:', error);
      submitLoading.value = false;
      proxy.$Message({message: "提交异常，请联系管理员", type: "error"});
    });
  }

  // 关闭页面
  function handleCancel() {
    proxy.$router.$avueRouter.closeTag();
    proxy.$router.push({ path: `/work/tasks` });
  }

  function init() {
    // 获取审批表单配置
    auditOption.value = {
      submitBtn: false,  // 隐藏提交按钮
      emptyBtn: false,   // 隐藏清空按钮
      labelWidth: 120,   // 标签宽度
      column: [
        {
          label: "审批结果",
          prop: "flag",
          type: "radio",
          span: 12,  // 设置为12列，占一半宽度
          dicData: [
            {
              label: "同意",
              value: "1"
            },
            {
              label: "驳回",
              value: "0"
            }
          ],
          rules: [
            {
              required: true,
              message: "请选择审批结果",
              trigger: "blur"
            }
          ],
          change: (val) => {
            let backTaskKeyOpt = proxy.findObject(auditOption.value.column, "backTaskKey");
            if (backTaskKeyOpt) {
              backTaskKeyOpt.display = val === "0";
            }
          }
        },
        {
          label: "驳回节点",
          prop: "backTaskKey",
          type: "select",
          display: false,
          dicData: computeTaskKeyList,
          span: 6,  // 调整为6列，更短
          rules: [
            {
              required: true,
              message: "请选择驳回节点",
              trigger: "blur"
            }
          ]
        },
        {
          label: "审批意见",
          prop: "comment",
          type: "textarea",
          span: 24,
          minRows: 3,
          maxRows: 5,
          rules: [
            {
              required: true,
              message: "请输入审批意见",
              trigger: "blur"
            }
          ]
        }
      ]
    };

    // 获取表单数据
    let flow = {
      taskId: props.taskId,
      processInstanceId: props.processInstanceId,
      businessId: props.businessId
    };

    auditFormDetail(flow).then(resp => {
      console.log('获取到的数据:', resp.data.data);
      form.value = resp.data.data;

      // 检查数据结构
      if (!form.value.flowApvContractPayInfo) {
        console.error('数据结构错误: 缺少 flowApvContractPayInfo 字段');
        proxy.$Message.error('数据加载失败：数据结构错误');
        return;
      }

      console.log('付款审批信息:', form.value.flowApvContractPayInfo);
      console.log('支付方式列表:', form.value.flowApvContractPayInfo.payDetailList);

      // 确保支付方式列表存在
      if (!form.value.flowApvContractPayInfo.payDetailList) {
        form.value.flowApvContractPayInfo.payDetailList = [];
      }

      // 强制触发视图更新
      proxy.$nextTick(() => {
        console.log('视图更新完成，当前表单数据:', form.value.flowApvContractPayInfo);
      });

      // 生成PDF预览
      generatePdfPreview();
    }).catch(error => {
      console.error('获取表单数据失败:', error);
      proxy.$Message.error('获取表单数据失败');
    });
  }

  // 监听表单数据变化
  watch(() => form.value.flowApvContractPayInfo, (newVal) => {
    if (newVal) {
      console.log('表单数据发生变化:', newVal);
    }
  }, { deep: true });

  onMounted(() => {
    init();
  });
</script>

<style lang="scss" scoped>
</style>
